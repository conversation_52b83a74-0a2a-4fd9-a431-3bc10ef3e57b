import type React from 'react';

import { createBrowserRouter, RouterProvider } from 'react-router-dom';

import { routes } from './config/routes';
import { RefactoredAppProviders } from './providers/RefactoredAppProviders';

// Create router with our route configuration and future flags
const router = createBrowserRouter(routes, {
  future: {
    v7_startTransition: true,
    v7_fetcherPersist: true,
    v7_normalizeFormMethod: true,
    v7_partialHydration: true,
    v7_relativeSplatPath: true,
    v7_skipActionErrorRevalidation: true,
  },
});

/**
 * Main application component that sets up the refactored provider structure
 * and router configuration for the application.
 *
 * Features:
 * - Uses RefactoredAppProviders for optimized context management
 * - Built-in error boundaries and suspense handling
 * - Optimized React Query configuration
 * - Enhanced performance through reduced provider nesting
 */
const App: React.FC = () => {
  return (
    <RefactoredAppProviders>
      <RouterProvider 
        router={router} 
        future={{
          v7_startTransition: true,
        }}
      />
    </RefactoredAppProviders>
  );
};

export default App;
